# Cryptodo Electron Desktop App

This document describes how to build and run the Cryptodo desktop application using Electron instead of Tauri.

## Overview

The Electron app provides a native desktop experience for macOS, Windows, and Linux. It wraps the Next.js frontend in an Electron container, providing:

- Native window management
- System integration
- Offline capabilities
- Cross-platform compatibility

## Development

### Prerequisites
- Node.js (LTS version)
- pnpm package manager

### Running in Development Mode

1. **Start the Next.js development server:**
   ```bash
   cd frontend
   pnpm dev
   ```

2. **In a separate terminal, start Electron:**
   ```bash
   cd frontend
   pnpm electron:dev
   ```

This will open the Electron app pointing to the Next.js dev server at `http://localhost:4545`.

### Development Features
- Hot reload from Next.js dev server
- DevTools automatically opened
- Real-time code changes

## Building for Production

### Quick Build
```bash
cd frontend
pnpm electron:build
```

### Manual Build Steps
```bash
cd frontend

# Set production environment
cp .env.electron.production .env.local

# Build Next.js static files
pnpm build

# Build Electron app
electron-builder --publish=never

# Restore original environment
mv .env.local.backup .env.local  # if backup exists
```

### Build Outputs
- **macOS**: `dist/mac/Cryptodo.app`
- **DMG Installer**: `dist/Cryptodo-*.dmg`
- **Windows**: `dist/win-unpacked/` and `dist/Cryptodo Setup *.exe`
- **Linux**: `dist/linux-unpacked/` and `dist/Cryptodo-*.AppImage`

## Configuration

### Environment Variables
- **Development**: Uses `.env.local` (connects to localhost backend)
- **Production**: Uses `.env.electron.production` (connects to production backend)

### Electron Configuration
Main configuration is in `electron-builder.json`:
- App ID: `com.cryptodo.app`
- Product Name: `Cryptodo`
- Window size: 1200x800 (minimum 800x600)
- Icons: Located in `electron/icons/`

### Window Settings
- Resizable: Yes
- Minimum size: 800x600
- Default size: 1200x800
- Center on screen: Yes
- Security: Context isolation enabled, node integration disabled

## Architecture

### Main Process (`electron/main.js`)
- Creates and manages application windows
- Handles app lifecycle events
- Sets up security policies
- Manages application menu

### Renderer Process
- Next.js application running in Electron's renderer process
- Same codebase as web version
- Uses standard Apollo Client (no Tauri-specific code)

### Communication
- Standard HTTP requests to GraphQL backend
- No special IPC needed for current functionality
- Uses same authentication flow as web version

## Security

The Electron app follows security best practices:
- Context isolation enabled
- Node integration disabled in renderer
- Web security enabled
- External links open in system browser
- CSP headers respected

## Distribution

### macOS
- **Development**: Run directly from `dist/mac/Cryptodo.app`
- **Distribution**: Use DMG installer from `dist/`
- **App Store**: Additional configuration needed for Mac App Store

### Windows
- **Development**: Run from `dist/win-unpacked/`
- **Distribution**: Use NSIS installer from `dist/`

### Linux
- **Development**: Run from `dist/linux-unpacked/`
- **Distribution**: Use AppImage from `dist/`

## Troubleshooting

### Common Issues

1. **App won't start**: Check that Next.js build completed successfully
2. **Blank screen**: Verify `out/` directory contains built files
3. **"Loading..." stuck**: Make sure you used `ELECTRON_BUILD=true` when building
4. **File not found errors**: Rebuild with `ELECTRON_BUILD=true pnpm build`
5. **API errors**: Check environment variables and backend connectivity
6. **Build failures**: Ensure all dependencies are installed

### Asset Path Issues

The app uses different asset paths for web vs Electron:
- **Web builds**: Use absolute paths (`/_next/...`)
- **Electron builds**: Use relative paths (`./next/...`)

This is controlled by the `ELECTRON_BUILD` environment variable.

### Debug Mode
```bash
# Build in debug mode (faster compilation)
pnpm electron:build:debug
```

### Local Build
```bash
# Build with current environment (for testing)
pnpm electron:build:local
```

## Migration from Tauri

This app was migrated from Tauri to Electron. Key changes:
- Replaced Rust backend with Node.js/Electron main process
- Removed Tauri-specific HTTP handling
- Updated build scripts and configuration
- Simplified Apollo Client (no more Tauri plugin)
- Same UI and functionality maintained

## Scripts Reference

- `pnpm electron:dev` - Development mode
- `pnpm electron:build` - Production build
- `pnpm electron:build:debug` - Debug production build
- `pnpm electron:build:local` - Local environment build
- `pnpm electron:pack` - Package without installer
- `pnpm electron` - Run built app directly
