{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Cryptodo", "version": "0.1.0", "identifier": "com.cryptodo.app", "build": {"frontendDist": "../out", "devUrl": "http://localhost:4545", "beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build"}, "app": {"windows": [{"label": "main", "title": "Cryptodo - Crypto Task Manager", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true}], "security": {"csp": "default-src blob: data: filesystem: ws: wss: http: https: tauri: 'unsafe-eval' 'unsafe-inline' 'self' img-src: 'self'", "dangerousDisableAssetCspModification": true, "capabilities": ["default"]}, "withGlobalTauri": false}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "category": "Productivity", "shortDescription": "A Trello-like task manager for crypto projects", "longDescription": "Cryptodo is a modern Trello-like application designed for managing crypto-related tasks and projects with specialized fields for EVM addresses and private keys."}}