{"appId": "com.cryptodo.app", "productName": "Cryptodo", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/main.js", "node_modules/**/*", "package.json"], "extraMetadata": {"main": "electron/main.js"}, "mac": {"category": "public.app-category.productivity", "icon": "electron/icons/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "minimumSystemVersion": "10.15.0"}, "dmg": {"title": "Cryptodo - Crypto Task Manager", "icon": "electron/icons/icon.icns"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "electron/icons/icon.ico"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "electron/icons/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}