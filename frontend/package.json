{"name": "cryptodo-frontend", "version": "0.1.0", "private": true, "main": "electron/main.js", "scripts": {"dev": "next dev -p 4545", "build": "next build", "build:web": "./build-web.sh", "start": "next start", "lint": "next lint", "electron": "electron electron/main.js", "electron:dev": "NODE_ENV=development electron electron/main.js", "electron:build": "./build-electron-production.sh", "electron:build:debug": "cp .env.electron.production .env.local && ELECTRON_BUILD=true pnpm build && electron-builder --publish=never", "electron:build:local": "ELECTRON_BUILD=true pnpm build && electron-builder --publish=never", "electron:pack": "ELECTRON_BUILD=true electron-builder --dir", "cap": "cap", "cap:add:ios": "cap add ios", "cap:sync": "cap sync", "cap:open:ios": "cap open ios", "ios:build": "pnpm build && cap sync && cap open ios", "ios:build:production": "cp .env.electron.production .env.local && pnpm build && cap sync && cap open ios"}, "dependencies": {"@apollo/client": "^3.8.0", "@react-oauth/google": "^0.12.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.4.5", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "graphql": "^16.7.1", "js-cookie": "^3.0.5", "next": "^13.4.12", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "react": "^18.2.0", "react-apple-signin-auth": "^1.1.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-markdown": "^8.0.7", "react-quill": "^2.0.0", "typescript": "^5.1.6"}, "devDependencies": {"@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@types/react-beautiful-dnd": "^13.1.4", "electron": "^36.3.1", "electron-builder": "^26.0.12", "eslint": "^8.46.0", "eslint-config-next": "^13.4.12", "sass": "^1.64.2"}}