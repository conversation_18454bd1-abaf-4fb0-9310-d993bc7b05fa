#!/bin/bash

# Build Electron app for production with correct environment variables

echo "🚀 Building Cryptodo Electron app for production..."

# Backup current .env.local if it exists
if [ -f .env.local ]; then
    echo "📦 Backing up current .env.local..."
    cp .env.local .env.local.backup
fi

# Copy production environment variables
echo "🔧 Setting up production environment..."
cp .env.electron.production .env.local

# Build the Next.js app with production settings
echo "🏗️  Building Next.js app..."
ELECTRON_BUILD=true pnpm build

# Build the Electron app
echo "📱 Building Electron desktop app..."
electron-builder --publish=never

# Restore original .env.local if backup exists
if [ -f .env.local.backup ]; then
    echo "🔄 Restoring original .env.local..."
    mv .env.local.backup .env.local
else
    echo "🧹 Cleaning up production .env.local..."
    rm .env.local
fi

echo "✅ Production build complete!"
echo "📍 App location: dist/"
echo "📱 macOS app: dist/mac/Cryptodo.app"
echo "💿 DMG installer: dist/Cryptodo-*.dmg"
