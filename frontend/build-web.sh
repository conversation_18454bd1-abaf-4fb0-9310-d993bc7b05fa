#!/bin/bash

# Build script for web deployment (Docker)

echo "🌐 Building Cryptodo for web deployment..."

# Backup current next.config.js
if [ -f next.config.js ]; then
    echo "📦 Backing up current next.config.js..."
    cp next.config.js next.config.js.backup
fi

# Use web-specific configuration
echo "🔧 Using web configuration..."
cp next.config.web.js next.config.js

# Build the Next.js app for web
echo "🏗️  Building Next.js app for web..."
pnpm build

# Restore original next.config.js if backup exists
if [ -f next.config.js.backup ]; then
    echo "🔄 Restoring original next.config.js..."
    mv next.config.js.backup next.config.js
fi

echo "✅ Web build complete!"
echo "📍 Ready for Docker deployment"
