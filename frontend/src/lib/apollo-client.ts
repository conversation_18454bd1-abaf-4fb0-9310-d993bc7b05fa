import { ApolloClient, InMemoryCache, HttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import Cookies from 'js-cookie';

const graphqlUrl = process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4544/graphql';

console.log('Apollo Client Configuration:', {
  graphqlUrl,
  nodeEnv: process.env.NODE_ENV,
  isProduction: process.env.NODE_ENV === 'production'
});

const httpLink = new HttpLink({
  uri: graphqlUrl,
  fetch: (uri, options) => {
    console.log('GraphQL Request:', { uri, options });
    return fetch(uri, options);
  },
});

const authLink = setContext((_, { headers }) => {
  // Get the authentication token from cookies
  const token = Cookies.get('token');

  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json',
    },
  };
});

const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error('GraphQL Error:', { message, locations, path });
    });
  }

  if (networkError) {
    console.error('Network Error:', networkError);
    console.error('Network Error Details:', {
      name: networkError.name,
      message: networkError.message,
      stack: networkError.stack,
    });
  }
});

const client = new ApolloClient({
  link: from([errorLink, authLink, httpLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
});

export default client;
