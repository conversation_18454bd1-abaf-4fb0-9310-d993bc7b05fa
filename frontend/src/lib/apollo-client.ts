import { ApolloClient, InMemoryCache, HttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import TokenStorage from './token-storage';

const graphqlUrl = process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4544/graphql';

console.log('Apollo Client Configuration:', {
  graphqlUrl,
  nodeEnv: process.env.NODE_ENV,
  isProduction: process.env.NODE_ENV === 'production'
});

// Check if we're running in Electron
const isElectron = () => {
  return typeof window !== 'undefined' &&
         (window as any).process &&
         (window as any).process.type === 'renderer';
};

console.log('Environment:', {
  isElectron: isElectron(),
  userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server'
});

const httpLink = new HttpLink({
  uri: graphqlUrl,
  fetch: (uri, options) => {
    console.log('GraphQL Request:', { uri, options });
    return fetch(uri, options);
  },
});

const authLink = setContext(async (_, { headers }) => {
  const token = await TokenStorage.getToken();

  console.log('Auth link - token check:', {
    hasToken: !!token,
    tokenPreview: token ? token.substring(0, 20) + '...' : 'none'
  });

  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json',
    },
  };
});

const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error('GraphQL Error:', { message, locations, path });
    });
  }

  if (networkError) {
    console.error('Network Error:', networkError);
    console.error('Network Error Details:', {
      name: networkError.name,
      message: networkError.message,
      stack: networkError.stack,
    });
  }
});

const client = new ApolloClient({
  link: from([errorLink, authLink, httpLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
});

export default client;
