// Tauri-specific Apollo Client using tauri-plugin-http
import { ApolloClient, InMemoryCache, ApolloLink, from, Observable } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import TokenStorage from './token-storage';

const graphqlUrl = process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4544/graphql';

console.log('Tauri Apollo Client Configuration:', {
  graphqlUrl,
  nodeEnv: process.env.NODE_ENV,
  isProduction: process.env.NODE_ENV === 'production'
});

// Check if we're running in Tauri
const isTauri = () => {
  return typeof window !== 'undefined' &&
         (window as any).__TAURI__ !== undefined;
};

// Custom HTTP link using Tauri's HTTP plugin
const createTauriHttpLink = () => {
  return new ApolloLink((operation) => {
    return new Observable((observer) => {
      const executeRequest = async () => {
        try {
          if (!isTauri()) {
            // Fallback to regular fetch for non-Tauri environments
            const response = await fetch(graphqlUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                ...operation.getContext().headers,
              },
              body: JSON.stringify({
                query: operation.query,
                variables: operation.variables,
                operationName: operation.operationName,
              }),
            });

            const result = await response.json();
            observer.next(result);
            observer.complete();
            return;
          }

          // Use Tauri HTTP plugin
          const { fetch: tauriFetch } = await import('@tauri-apps/plugin-http');

          console.log('Making Tauri HTTP request to:', graphqlUrl);
          console.log('Request headers:', operation.getContext().headers);

          const response = await tauriFetch(graphqlUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...operation.getContext().headers,
            },
            body: JSON.stringify({
              query: operation.query,
              variables: operation.variables,
              operationName: operation.operationName,
            }),
          });

          console.log('Tauri HTTP response status:', response.status);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          console.log('Tauri HTTP response data:', result);
          observer.next(result);
          observer.complete();
        } catch (error) {
          console.error('Tauri HTTP request failed:', error);
          observer.error(error);
        }
      };

      executeRequest();
    });
  });
};

const authLink = setContext(async (_, { headers }) => {
  // Get the authentication token from cross-platform storage
  const token = await TokenStorage.getToken();

  console.log('Tauri Auth link - token check:', {
    hasToken: !!token,
    tokenPreview: token ? token.substring(0, 20) + '...' : 'none'
  });

  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json',
    },
  };
});

const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error('GraphQL Error:', { message, locations, path });
    });
  }

  if (networkError) {
    console.error('Network Error:', networkError);
    console.error('Network Error Details:', {
      name: networkError.name,
      message: networkError.message,
      stack: networkError.stack,
    });
  }
});

const tauriClient = new ApolloClient({
  link: from([errorLink, authLink, createTauriHttpLink()]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
});

export default tauriClient;
