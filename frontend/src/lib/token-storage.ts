// Cross-platform token storage for web browsers, Electron, and Capacitor apps

import Cookies from 'js-cookie';

// Check if we're running in Electron
const isElectron = () => {
  return typeof window !== 'undefined' &&
         (window as any).process &&
         (window as any).process.type === 'renderer';
};

// Check if we're running in a Capacitor app
const isCapacitor = () => {
  return typeof window !== 'undefined' &&
         (window as any).Capacitor !== undefined;
};

// Check if we're in a native app environment
const isNativeApp = () => {
  return isElectron() || isCapacitor();
};

export const TokenStorage = {
  // Set authentication token
  setToken: async (token: string, expirationDays: number = 7) => {
    console.log('Setting token:', {
      token: token.substring(0, 20) + '...',
      tokenLength: token.length,
      isElectron: isElectron(),
      isCapacitor: isCapacitor(),
      isNativeApp: isNativeApp(),
      expirationDays
    });

    // For Electron, prefer localStorage as primary storage since cookies can be unreliable
    if (isElectron()) {
      try {
        const expirationTime = new Date().getTime() + (expirationDays * 24 * 60 * 60 * 1000);
        const tokenData = {
          token,
          expiration: expirationTime
        };
        localStorage.setItem('auth_token', JSON.stringify(tokenData));
        console.log('Token stored in localStorage for Electron');

        // Also try to set cookie as backup
        try {
          const cookieOptions: any = {
            expires: expirationDays,
            secure: false,
            sameSite: 'lax',
            path: '/'
          };
          Cookies.set('token', token, cookieOptions);
          console.log('Token also stored in cookies as backup for Electron');
        } catch (cookieError) {
          console.warn('Failed to store token in cookies for Electron:', cookieError);
        }
      } catch (error) {
        console.error('Failed to store token in localStorage for Electron:', error);
      }
    } else {
      // For web browsers and Capacitor, use cookies as primary
      const cookieOptions: any = {
        expires: expirationDays,
        secure: location.protocol === 'https:',
        sameSite: 'lax',
        path: '/'
      };

      Cookies.set('token', token, cookieOptions);
      console.log('Token stored in cookies for web/Capacitor');

      // Also store in localStorage as backup for Capacitor
      if (isCapacitor()) {
        try {
          const expirationTime = new Date().getTime() + (expirationDays * 24 * 60 * 60 * 1000);
          const tokenData = {
            token,
            expiration: expirationTime
          };
          localStorage.setItem('auth_token', JSON.stringify(tokenData));
          console.log('Token also stored in localStorage as backup for Capacitor');
        } catch (error) {
          console.error('Failed to store token in localStorage backup for Capacitor:', error);
        }
      }
    }
  },

  // Get authentication token
  getToken: async (): Promise<string | null> => {
    // For Electron, try localStorage first since it's more reliable
    if (isElectron()) {
      try {
        const tokenDataStr = localStorage.getItem('auth_token');
        if (tokenDataStr) {
          const tokenData = JSON.parse(tokenDataStr);

          // Check if token is expired
          if (tokenData.expiration && new Date().getTime() > tokenData.expiration) {
            console.log('Token expired, removing from localStorage');
            localStorage.removeItem('auth_token');
            // Also remove from cookies
            Cookies.remove('token');
            return null;
          }

          console.log('Token retrieved from localStorage for Electron');
          return tokenData.token;
        }
      } catch (error) {
        console.error('Failed to retrieve token from localStorage for Electron:', error);
      }

      // Fallback to cookies for Electron
      const cookieToken = Cookies.get('token');
      if (cookieToken) {
        console.log('Token retrieved from cookies fallback for Electron');
        return cookieToken;
      }
    } else {
      // For web browsers and Capacitor, try cookies first
      const cookieToken = Cookies.get('token');
      if (cookieToken) {
        console.log('Token retrieved from cookies for web/Capacitor');
        return cookieToken;
      }

      // Try localStorage backup for Capacitor
      if (isCapacitor()) {
        try {
          const tokenDataStr = localStorage.getItem('auth_token');
          if (tokenDataStr) {
            const tokenData = JSON.parse(tokenDataStr);

            // Check if token is expired
            if (tokenData.expiration && new Date().getTime() > tokenData.expiration) {
              console.log('Token expired, removing from localStorage backup');
              localStorage.removeItem('auth_token');
              return null;
            }

            console.log('Token retrieved from localStorage backup for Capacitor');
            return tokenData.token;
          }
        } catch (error) {
          console.error('Failed to retrieve token from localStorage backup for Capacitor:', error);
        }
      }
    }

    console.log('No token found in any storage');
    return null;
  },

  // Remove authentication token
  removeToken: async () => {
    console.log('Removing token from all storage methods');

    // Remove from cookies
    Cookies.remove('token');
    console.log('Token removed from cookies');

    // Remove from localStorage for native apps
    if (isNativeApp()) {
      try {
        localStorage.removeItem('auth_token');
        console.log('Token removed from localStorage');
      } catch (error) {
        console.error('Failed to remove token from localStorage:', error);
      }
    }
  },

  // Check if token exists
  hasToken: async (): Promise<boolean> => {
    const token = await TokenStorage.getToken();
    return token !== null;
  }
};

export default TokenStorage;
