// Cross-platform token storage for web browsers, Electron, and Capacitor apps

import Cookies from 'js-cookie';

// Check if we're running in Electron
const isElectron = () => {
  return typeof window !== 'undefined' &&
         (window as any).process &&
         (window as any).process.type === 'renderer';
};

// Check if we're running in a Capacitor app
const isCapacitor = () => {
  return typeof window !== 'undefined' &&
         (window as any).Capacitor !== undefined;
};

// Check if we're in a native app environment
const isNativeApp = () => {
  return isElectron() || isCapacitor();
};

export const TokenStorage = {
  // Set authentication token
  setToken: async (token: string, expirationDays: number = 7) => {
    console.log('Setting token:', {
      token: token.substring(0, 20) + '...',
      isTauri: isTauri(),
      isCapacitor: isCapacitor(),
      isNativeApp: isNativeApp()
    });

    // Use cookies with Tauri HTTP plugin compatible settings
    const cookieOptions: any = {
      expires: expirationDays,
      secure: false, // Required for Tauri HTTP plugin
      sameSite: 'none', // Required for cross-origin requests in Tauri
      path: '/' // Ensure cookie is available for all paths
    };

    Cookies.set('token', token, cookieOptions);
    console.log('Token stored in cookies with Tauri HTTP plugin settings');

    // Also store in localStorage as backup for native apps
    if (isNativeApp()) {
      try {
        const expirationTime = new Date().getTime() + (expirationDays * 24 * 60 * 60 * 1000);
        const tokenData = {
          token,
          expiration: expirationTime
        };
        localStorage.setItem('auth_token', JSON.stringify(tokenData));
        console.log('Token also stored in localStorage as backup');
      } catch (error) {
        console.error('Failed to store token in localStorage backup:', error);
      }
    }
  },

  // Get authentication token
  getToken: async (): Promise<string | null> => {
    // Try cookies first (should work with Tauri HTTP plugin)
    const cookieToken = Cookies.get('token');
    if (cookieToken) {
      console.log('Token retrieved from cookies');
      return cookieToken;
    }

    // Try localStorage backup for native apps
    if (isNativeApp()) {
      try {
        const tokenDataStr = localStorage.getItem('auth_token');
        if (tokenDataStr) {
          const tokenData = JSON.parse(tokenDataStr);

          // Check if token is expired
          if (tokenData.expiration && new Date().getTime() > tokenData.expiration) {
            console.log('Token expired, removing from localStorage backup');
            localStorage.removeItem('auth_token');
            return null;
          }

          console.log('Token retrieved from localStorage backup');
          return tokenData.token;
        }
      } catch (error) {
        console.error('Failed to retrieve token from localStorage backup:', error);
      }
    }

    console.log('No token found in any storage');
    return null;
  },

  // Remove authentication token
  removeToken: async () => {
    console.log('Removing token from all storage methods');

    // Remove from cookies
    Cookies.remove('token');
    console.log('Token removed from cookies');

    // Remove from localStorage backup for native apps
    if (isNativeApp()) {
      try {
        localStorage.removeItem('auth_token');
        console.log('Token removed from localStorage backup');
      } catch (error) {
        console.error('Failed to remove token from localStorage backup:', error);
      }
    }
  },

  // Check if token exists
  hasToken: async (): Promise<boolean> => {
    const token = await TokenStorage.getToken();
    return token !== null;
  }
};

export default TokenStorage;
