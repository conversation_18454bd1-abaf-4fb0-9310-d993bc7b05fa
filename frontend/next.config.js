/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Disabled to fix issues with react-beautiful-dnd
  swcMinify: true,
  images: {
    domains: ['localhost', 'cryptodo.dsserv.de'],
    unoptimized: true, // Required for static export
  },
  output: 'export', // Static export for Electron and Capacitor
  trailingSlash: true,
  distDir: 'out', // Ensure output goes to 'out' directory
  // Use relative paths for assets in Electron (set by environment variable)
  assetPrefix: process.env.ELECTRON_BUILD === 'true' ? '.' : undefined,
}

module.exports = nextConfig
