# Nginx configuration for CryptoDo on cryptodo.dsserv.de
# This is optional - you can use this if you want to set up a reverse proxy

server {
    listen 80;
    server_name cryptodo.dsserv.de;

    # Redirect HTTP to HTTPS (optional)
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name cryptodo.dsserv.de;

    # Increase client body size limit for image uploads and large GraphQL requests
    client_max_body_size 50M;

    # SSL configuration (you'll need to add your SSL certificates)
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;

    # Frontend (Next.js)
    location / {
        proxy_pass http://localhost:4545;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend GraphQL API
    location /graphql {
        proxy_pass http://localhost:4544/graphql;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Pass through the Origin header to the backend
        proxy_set_header Origin $http_origin;

        # Let the backend handle CORS instead of nginx
        # Remove nginx CORS headers to avoid conflicts

        # Handle OPTIONS requests by passing them to the backend
        # The backend will handle CORS for all origins including tauri://localhost
    }

    # API routes (if you add any REST endpoints)
    location /api/ {
        proxy_pass http://localhost:4544/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
