# Docker Build Fix for Next.js Configuration

## 🎯 Problem Identified

The Docker build failed because:
- Next.js config was changed to `output: 'export'` for Tauri/Capacitor
- Docker expects `output: 'standalone'` for web deployment
- The `.next/standalone` directory doesn't exist with static export

**Error**: `"/app/.next/standalone": not found`

## ✅ Solution Applied

I've created a dual-configuration setup:

### Configuration Files
- `next.config.js` - For Tauri/Capacitor (static export)
- `next.config.web.js` - For Docker web deployment (standalone)

### Build Scripts
- `pnpm build` - Static export for native apps
- `pnpm build:web` - Standalone build for web deployment
- `./build-web.sh` - Automated web build script

## 🚀 Deploy the Fix

### Option 1: Quick Deploy (Recommended)

```bash
# On your production server
cd /path/to/cryptodo
git pull origin main

# Deploy with the fixed configuration
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d --build
```

### Option 2: Manual Build Test

Test the build locally first:

```bash
# Test the web build
cd frontend
pnpm build:web

# Test Docker build
docker build -t cryptodo-frontend .
```

## 🔧 How the Fix Works

### 1. Dockerfile Changes
```dockerfile
# Use web-specific Next.js config for Docker builds
RUN cp next.config.web.js next.config.js
RUN pnpm run build
```

### 2. Configuration Separation
**For Native Apps** (`next.config.js`):
```javascript
output: 'export',     // Static files for Tauri/Capacitor
distDir: 'out',       // Output to 'out' directory
```

**For Web Deployment** (`next.config.web.js`):
```javascript
output: 'standalone', // Server bundle for Docker
// No distDir override
```

### 3. Build Process
- **Native apps**: Use `pnpm tauri:build` (uses static export)
- **Web deployment**: Docker uses web config automatically
- **Development**: Uses default config

## 🧪 Verify the Fix

### Test 1: Check Build Output
After running the build, you should see:
```
✅ Web build complete!
📍 Ready for Docker deployment
```

### Test 2: Verify Docker Build
```bash
cd frontend
docker build -t test-frontend .
```

Should complete without the "standalone not found" error.

### Test 3: Check File Structure
After web build:
```
frontend/
├── .next/
│   ├── standalone/     ✅ Should exist for Docker
│   └── static/         ✅ Should exist
└── out/               ✅ Should exist for native apps
```

## 📋 Updated Build Commands

### For Development
```bash
pnpm dev                    # Development server
```

### For Native Apps
```bash
pnpm tauri:build           # Desktop app (production)
pnpm ios:build:production  # iOS app (production)
```

### For Web Deployment
```bash
pnpm build:web             # Web build for Docker
docker-compose up --build  # Deploy to production
```

## 🔍 Troubleshooting

### If Docker Build Still Fails

1. **Clear Docker cache**:
   ```bash
   docker system prune -a
   docker-compose build --no-cache
   ```

2. **Check file exists**:
   ```bash
   cd frontend
   pnpm build:web
   ls -la .next/standalone/  # Should show server.js
   ```

3. **Verify config is correct**:
   ```bash
   cat next.config.web.js    # Should show output: 'standalone'
   ```

### If Native Apps Break

The native app builds should still work:
```bash
pnpm tauri:build          # Should work normally
```

If not, check that `next.config.js` still has `output: 'export'`.

## 🎯 Expected Results

After this fix:
- ✅ Docker builds succeed without errors
- ✅ Web deployment works normally  
- ✅ Tauri desktop app builds work
- ✅ iOS Capacitor app builds work
- ✅ All configurations coexist peacefully

## 📞 Deploy Now

Run this on your production server:

```bash
cd /path/to/cryptodo
git pull origin main
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d --build
```

The Docker build error should be completely resolved! 🎉
